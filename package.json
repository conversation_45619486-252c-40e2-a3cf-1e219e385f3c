{"name": "dify2openai", "version": "1.0.0", "description": "turn dify api into openai", "main": "app.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "NODE_ENV=production node app.js", "dev": "NODE_ENV=development nodemon app.js", "pm2:start": "pm2 start ecosystem.config.cjs", "pm2:stop": "pm2 stop dify2openai", "pm2:restart": "pm2 restart dify2openai", "pm2:delete": "pm2 delete dify2openai", "pm2:logs": "pm2 logs dify2openai", "pm2:monit": "pm2 monit"}, "keywords": [], "author": "orence", "license": "MIT", "dependencies": {"body-parser": "^2.2.0", "dotenv": "^16.4.7", "express": "^5.1.0", "form-data": "^4.0.2", "form-data-encoder": "^4.0.2", "formdata-node": "^6.0.3", "node-fetch": "^3.3.2", "tail": "^2.2.6", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.18.1"}, "devDependencies": {"nodemon": "^3.1.9"}, "packageManager": "pnpm@9.15.1+sha512.1acb565e6193efbebda772702950469150cf12bcc764262e7587e71d19dc98a423dff9536e57ea44c49bdf790ff694e83c27be5faa23d67e0c033b583be4bfcf"}