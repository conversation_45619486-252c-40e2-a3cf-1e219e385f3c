<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Dify2OpenAI Gateway - 将Dify应用转换为OpenAI API接口的网关服务">
  <title>Dify2OpenAI Gateway</title>
  <link rel="shortcut icon" href="https://io.onenov.cn/file/202504050505441.ico" type="image/x-icon">
  <!-- 引入TailwindCSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- 引入字体图标 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Prism.js 用于代码高亮 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#3b82f6',
            secondary: '#4b5563',
            dify: '#38bdf8'
          },
          animation: {
            'bounce-slow': 'bounce 3s infinite',
          }
        }
      }
    }
  </script>
  <style>
    /* 平滑滚动 */
    html {
      scroll-behavior: smooth;
    }

    /* 自定义滚动条 */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 8px;
    }

    ::-webkit-scrollbar-thumb {
      background: #c5c5c5;
      border-radius: 8px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: #a0aec0;
    }

    /* 背景渐变 */
    body {
      background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
    }

    /* 可编辑的样式 */
    .editable {
      border-bottom: 1px dashed #a0aec0;
      padding: 2px 4px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .editable:hover {
      background-color: rgba(59, 130, 246, 0.1);
      border-bottom: 1px dashed #3b82f6;
    }

    .editable:focus {
      outline: none;
      background-color: rgba(59, 130, 246, 0.1);
      border-bottom: 1px solid #3b82f6;
    }
  </style>
</head>

<body class="text-gray-800 min-h-screen antialiased">
  <header class="bg-white shadow-sm sticky top-0 z-50">
    <div class="max-w-6xl mx-auto px-4 py-3 sm:px-6 lg:px-8 flex justify-between items-center">
      <div class="flex items-center space-x-2">
        <i class="fas fa-exchange-alt text-blue-500"></i>
        <span class="font-bold text-xl text-blue-700">Dify2OpenAI</span>
      </div>
      <div class="flex space-x-4">
        <a href="https://github.com/onenov/Dify2OpenAI"
          class="flex items-center text-gray-600 hover:text-blue-600 transition-colors">
          <i class="fab fa-github mr-1"></i>
          <span>GitHub</span>
        </a>
      </div>
  </header>

  <div class="max-w-6xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
    <!-- 标题区 -->
    <div class="mb-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg p-10 text-white">
      <div class="mx-auto">
        <h1 class="text-4xl font-bold mb-4 flex items-center">
          <i class="fas fa-exchange-alt mr-3 text-blue-200"></i>
          Dify2OpenAI Gateway
        </h1>
        <p class="text-xl opacity-90">
          Dify2OpenAI 是一个将 Dify 应用程序转换为 OpenAI API 接口的网关服务，使您可以使用 OpenAI API 兼容的方式与 Dify 应用进行交互。
        </p>
        <div class="mt-6 flex flex-wrap gap-4">
          <a href="#config"
            class="bg-white text-blue-600 hover:bg-blue-50 transition-colors rounded-full px-5 py-2 font-semibold flex items-center">
            <i class="fas fa-cog mr-2"></i>配置参数
          </a>
          <a href="#access"
            class="bg-white/20 hover:bg-white/30 transition-colors rounded-full px-5 py-2 font-semibold flex items-center">
            <i class="fas fa-plug mr-2"></i>接入方式
          </a>
        </div>
      </div>
    </div>

    <!-- 配置参数 -->
    <section id="config" class="mb-12 scroll-mt-20">
      <h2 class="text-2xl font-bold text-gray-800 mb-6 border-l-4 border-blue-500 pl-3 flex items-center">
        <i class="fas fa-cog mr-2 text-blue-500"></i>配置参数
      </h2>
      <div class="overflow-x-auto rounded-lg shadow">
        <table class="min-w-full bg-white">
          <thead>
            <tr class="bg-blue-50 text-left text-gray-700">
              <th class="py-3 px-4 text-sm font-medium uppercase tracking-wider">参数</th>
              <th class="py-3 px-4 text-sm font-medium uppercase tracking-wider">说明</th>
              <th class="py-3 px-4 text-sm font-medium uppercase tracking-wider">示例</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr class="hover:bg-gray-50">
              <td class="py-3 px-4 font-medium">DIFY_API_URL</td>
              <td class="py-3 px-4">Dify 服务的 API 基础 URL</td>
              <td class="py-3 px-4 font-mono text-sm text-blue-600">
                <span class="editable" contenteditable="true" id="dify-api-url">https://cloud.dify.ai/v1</span>
              </td>
            </tr>
            <tr class="hover:bg-gray-50">
              <td class="py-3 px-4 font-medium">API_KEY</td>
              <td class="py-3 px-4">Dify 应用的 API 密钥</td>
              <td class="py-3 px-4 font-mono text-sm text-blue-600">
                <span class="editable" contenteditable="true" id="api-key">app-xxxx</span>
              </td>
            </tr>
            <tr class="hover:bg-gray-50">
              <td class="py-3 px-4 font-medium">BOT_TYPE</td>
              <td class="py-3 px-4">应用类型（Chat、Completion、Workflow）</td>
              <td class="py-3 px-4 font-mono text-sm text-blue-600">
                <span class="editable" contenteditable="true" id="bot-type">Chat</span>
              </td>
            </tr>
            <tr class="hover:bg-gray-50">
              <td class="py-3 px-4 font-medium">INPUT_VARIABLE</td>
              <td class="py-3 px-4">输入变量名称（仅 Workflow 类型需要）</td>
              <td class="py-3 px-4 font-mono text-sm text-blue-600">
                <span class="editable" contenteditable="true" id="input-variable">input</span>
              </td>
            </tr>
            <tr class="hover:bg-gray-50">
              <td class="py-3 px-4 font-medium">OUTPUT_VARIABLE</td>
              <td class="py-3 px-4">输出变量名称（仅 Workflow 类型需要）</td>
              <td class="py-3 px-4 font-mono text-sm text-blue-600">
                <span class="editable" contenteditable="true" id="output-variable">output</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </section>

    <!-- 接入方式 -->
    <section id="access" class="mb-12 scroll-mt-20">
      <h2 class="text-2xl font-bold text-gray-800 mb-6 border-l-4 border-blue-500 pl-3 flex items-center">
        <i class="fas fa-plug mr-2 text-blue-500"></i>接入方式
      </h2>

      <!-- 方式一 -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-8 hover:shadow-lg transition-shadow duration-300">
        <h3 class="text-xl font-semibold text-blue-700 mb-4 flex items-center">
          <i class="fas fa-key mr-2"></i>方式一：所有配置在 Authorization Header 中
        </h3>
        <div class="mb-4">
          <p class="mb-2 font-medium">✔️ Authorization Header 格式：</p>
          <div class="bg-gray-100 p-3 rounded-md font-mono text-sm overflow-x-auto">
            Authorization: Bearer DIFY_API_URL|API_KEY|BOT_TYPE|INPUT_VARIABLE|OUTPUT_VARIABLE
          </div>
        </div>
        <div class="mb-4">
          <p class="mb-2 font-medium">✔️ 示例：</p>
          <div class="bg-gray-100 p-3 rounded-md font-mono text-sm overflow-x-auto">
            Authorization: Bearer https://cloud.dify.ai/v1|app-xxxx|Chat
          </div>
        </div>
        <div class="mb-2 bg-blue-50 p-4 rounded-md border-l-4 border-blue-400">
          <p><code class="font-mono bg-blue-100 px-1 py-0.5 rounded text-blue-800">model</code> 参数设置为 <code
              class="font-mono bg-blue-100 px-1 py-0.5 rounded text-blue-800">dify</code></p>
        </div>
      </div>

      <div class="mt-6 mb-6">
        <div
          class="bg-gradient-to-r from-blue-50 to-gray-50 rounded-t-lg p-4 border border-gray-200 flex justify-between items-center">
          <h4 class="font-bold text-gray-700">基础对话示例</h4>
          <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">方式一</span>
        </div>
        <div class="bg-gray-900 text-gray-100 rounded-b-lg overflow-x-auto">
          <pre class="p-4 text-sm font-mono leading-relaxed"><span class="text-pink-400">curl</span> http://localhost:3099/v1/chat/completions \
  -H <span class="text-green-400">"Content-Type: application/json"</span> \
  -H <span class="text-green-400">"Authorization: Bearer https://cloud.dify.ai/v1|app-xxxx|Chat"</span> \
  -X <span class="text-yellow-400">POST</span> \
  -d <span class="text-green-400">'{
    "model": "dify",
    "stream": true,
    "messages": [
      {
        "role": "system",
        "content": "You are a helpful assistant."
      },
      {
        "role": "user",
        "content": "你好"
      }
    ]
  }'</span></pre>
        </div>
      </div>

      <div class="mb-8">
        <div
          class="bg-gradient-to-r from-purple-50 to-gray-50 rounded-t-lg p-4 border border-gray-200 flex justify-between items-center">
          <h4 class="font-bold text-gray-700">带图片的对话示例</h4>
          <span class="text-xs px-2 py-1 bg-purple-100 text-purple-800 rounded-full">图像支持</span>
        </div>
        <div class="bg-gray-900 text-gray-100 rounded-b-lg overflow-x-auto">
          <pre class="p-4 text-sm font-mono leading-relaxed"><span class="text-pink-400">curl</span> http://localhost:3099/v1/chat/completions \
  -H <span class="text-green-400">"Content-Type: application/json"</span> \
  -H <span class="text-green-400">"Authorization: Bearer https://cloud.dify.ai/v1|app-xxxx|Chat"</span> \
  -X <span class="text-yellow-400">POST</span> \
  -d <span class="text-green-400">'{
    "model": "dify",
    "stream": true,
    "messages": [
      {
        "role": "user",
        "content": [
          "请分析这张图片。",
          {
            "type": "image_url",
            "image_url": {
              "url": "https://example.com/image.jpg"
            }
          }
        ]
      }
    ]
  }'</span></pre>
        </div>
      </div>

      <!-- 方式二 -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-8 hover:shadow-lg transition-shadow duration-300">
        <h3 class="text-xl font-semibold text-indigo-700 mb-4 flex items-center">
          <i class="fas fa-key mr-2"></i>方式二：Authorization Header 传递 API_KEY，model 参数传递其他配置
        </h3>
        <div class="mb-4">
          <p class="mb-2 font-medium">✔️ Authorization Header 格式：</p>
          <div class="bg-gray-100 p-3 rounded-md font-mono text-sm overflow-x-auto">
            Authorization: Bearer API_KEY
          </div>
        </div>
        <div class="mb-4">
          <p class="mb-2 font-medium">✔️ model 参数格式：</p>
          <div class="bg-gray-100 p-3 rounded-md font-mono text-sm overflow-x-auto">
            "model": "dify|BOT_TYPE|DIFY_API_URL|INPUT_VARIABLE|OUTPUT_VARIABLE"
          </div>
        </div>
      </div>

      <div class="mb-6">
        <div
          class="bg-gradient-to-r from-indigo-50 to-gray-50 rounded-t-lg p-4 border border-gray-200 flex justify-between items-center">
          <h4 class="font-bold text-gray-700">基础对话示例</h4>
          <span class="text-xs px-2 py-1 bg-indigo-100 text-indigo-800 rounded-full">方式二</span>
        </div>
        <div class="bg-gray-900 text-gray-100 rounded-b-lg overflow-x-auto">
          <pre class="p-4 text-sm font-mono leading-relaxed"><span class="text-pink-400">curl</span> http://localhost:3099/v1/chat/completions \
  -H <span class="text-green-400">"Content-Type: application/json"</span> \
  -H <span class="text-green-400">"Authorization: Bearer app-xxxx"</span> \
  -X <span class="text-yellow-400">POST</span> \
  -d <span class="text-green-400">'{  
    "model": "dify|Chat|https://cloud.dify.ai/v1",
    "stream": true,
    "messages": [
      {
        "role": "system",
        "content": "You are a helpful assistant."
      },
      {
        "role": "user",
        "content": "你好"
      }
    ]
  }'</span></pre>
        </div>
      </div>

      <div class="mb-8">
        <div
          class="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-t-lg p-4 border border-gray-200 flex justify-between items-center">
          <h4 class="font-bold text-gray-700">带图片的对话示例</h4>
          <span class="text-xs px-2 py-1 bg-purple-100 text-purple-800 rounded-full">图像支持</span>
        </div>
        <div class="bg-gray-900 text-gray-100 rounded-b-lg overflow-x-auto">
          <pre class="p-4 text-sm font-mono leading-relaxed"><span class="text-pink-400">curl</span> http://localhost:3099/v1/chat/completions \
  -H <span class="text-green-400">"Content-Type: application/json"</span> \
  -H <span class="text-green-400">"Authorization: Bearer app-xxxx"</span> \
  -X <span class="text-yellow-400">POST</span> \
  -d <span class="text-green-400">'{  
    "model": "dify|Chat|https://cloud.dify.ai/v1",
    "stream": true,
    "messages": [
      {
        "role": "user",
        "content": [
          "请分析这张图片。",
          {
            "type": "image_url",
            "image_url": {
              "url": "https://example.com/image.jpg"
            }
          }
        ]
      }
    ]
  }'</span></pre>
        </div>
      </div>

      <!-- 方式三 -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-8 hover:shadow-lg transition-shadow duration-300">
        <h3 class="text-xl font-semibold text-teal-700 mb-4 flex items-center">
          <i class="fas fa-key mr-2"></i>方式三：Authorization Header 传递 DIFY_API_URL，model 参数传阒其他配置
        </h3>
        <div class="mb-4">
          <p class="mb-2 font-medium">✔️ Authorization Header 格式：</p>
          <div class="bg-gray-100 p-3 rounded-md font-mono text-sm overflow-x-auto">
            Authorization: Bearer DIFY_API_URL
          </div>
        </div>
        <div class="mb-4">
          <p class="mb-2 font-medium">✔️ model 参数格式：</p>
          <div class="bg-gray-100 p-3 rounded-md font-mono text-sm overflow-x-auto">
            "model": "dify|API_KEY|BOT_TYPE|INPUT_VARIABLE|OUTPUT_VARIABLE"
          </div>
        </div>
      </div>

      <div class="mb-6">
        <div
          class="bg-gradient-to-r from-teal-50 to-gray-50 rounded-t-lg p-4 border border-gray-200 flex justify-between items-center">
          <h4 class="font-bold text-gray-700">基础对话示例</h4>
          <span class="text-xs px-2 py-1 bg-teal-100 text-teal-800 rounded-full">方式三</span>
        </div>
        <div class="bg-gray-900 text-gray-100 rounded-b-lg overflow-x-auto">
          <pre class="p-4 text-sm font-mono leading-relaxed"><span class="text-pink-400">curl</span> http://localhost:3099/v1/chat/completions \
  -H <span class="text-green-400">"Content-Type: application/json"</span> \
  -H <span class="text-green-400">"Authorization: Bearer https://cloud.dify.ai/v1"</span> \
  -X <span class="text-yellow-400">POST</span> \
  -d <span class="text-green-400">'{  
    "model": "dify|app-xxxx|Chat",
    "stream": true,
    "messages": [
      {
        "role": "system",
        "content": "You are a helpful assistant."
      },
      {
        "role": "user",
        "content": "你好"
      }
    ]
  }'</span></pre>
        </div>
      </div>

      <div class="mb-8">
        <div
          class="bg-gradient-to-r from-purple-50 to-teal-50 rounded-t-lg p-4 border border-gray-200 flex justify-between items-center">
          <h4 class="font-bold text-gray-700">带图片的对话示例</h4>
          <span class="text-xs px-2 py-1 bg-purple-100 text-purple-800 rounded-full">图像支持</span>
        </div>
        <div class="bg-gray-900 text-gray-100 rounded-b-lg overflow-x-auto">
          <pre class="p-4 text-sm font-mono leading-relaxed"><span class="text-pink-400">curl</span> http://localhost:3099/v1/chat/completions \
  -H <span class="text-green-400">"Content-Type: application/json"</span> \
  -H <span class="text-green-400">"Authorization: Bearer https://cloud.dify.ai/v1"</span> \
  -X <span class="text-yellow-400">POST</span> \
  -d <span class="text-green-400">'{  
    "model": "dify|app-xxxx|Chat",
    "stream": true,
    "messages": [
      {
        "role": "user",
        "content": [
          "请分析这张图片。",
          {
            "type": "image_url",
            "image_url": {
              "url": "https://example.com/image.jpg"
            }
          }
        ]
      }
    ]
  }'</span></pre>
        </div>
      </div>
    </section>

    <!-- 功能特性区域 -->
    <section class="my-16">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
          <div class="text-blue-500 mb-4 text-3xl">
            <i class="fas fa-exchange-alt"></i>
          </div>
          <h3 class="text-lg font-semibold mb-2">接口转换</h3>
          <p class="text-gray-600">将 Dify API 无缝转换为 OpenAI API，兼容各种 OpenAI 客户端</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
          <div class="text-indigo-500 mb-4 text-3xl">
            <i class="fas fa-image"></i>
          </div>
          <h3 class="text-lg font-semibold mb-2">多模态支持</h3>
          <p class="text-gray-600">支持图像、文档、音频和视频等多种类型文件处理</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
          <div class="text-green-500 mb-4 text-3xl">
            <i class="fas fa-robot"></i>
          </div>
          <h3 class="text-lg font-semibold mb-2">多种应用类型</h3>
          <p class="text-gray-600">支持 Chat、Completion、Workflow 等不同类型的 Dify 应用</p>
        </div>
      </div>
    </section>

    <!-- 页脚区域 -->
    <footer class="mt-20 pt-10 border-t border-gray-200 text-gray-600">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-10">
        <div>
          <h3 class="text-lg font-semibold mb-4 flex items-center">
            <i class="fas fa-exchange-alt mr-2 text-blue-500"></i>Dify2OpenAI
          </h3>
          <p class="text-sm text-gray-500">将 Dify 应用程序转换为 OpenAI API 接口的网关服务</p>
        </div>
        <div>
          <h3 class="text-lg font-semibold mb-4">快速链接</h3>
          <ul class="space-y-2">
            <li><a href="#config" class="text-gray-500 hover:text-blue-500 transition-colors flex items-center"><i
                  class="fas fa-cog mr-2"></i>配置参数</a></li>
            <li><a href="#access" class="text-gray-500 hover:text-blue-500 transition-colors flex items-center"><i
                  class="fas fa-plug mr-2"></i>接入方式</a></li>
          </ul>
        </div>
        <div>
          <h3 class="text-lg font-semibold mb-4">资源链接</h3>
          <ul class="space-y-2">
            <li><a href="https://github.com/onenov/Dify2OpenAI"
                class="text-gray-500 hover:text-blue-500 transition-colors flex items-center"><i
                  class="fab fa-github mr-2"></i>GitHub 仓库</a></li>
            <li><a href="https://github.com/onenov/Dify2OpenAI/issues"
                class="text-gray-500 hover:text-blue-500 transition-colors flex items-center"><i
                  class="fas fa-bug mr-2"></i>问题反馈</a></li>
          </ul>
        </div>
      </div>
      <div class="flex flex-col sm:flex-row justify-between items-center border-t border-gray-200 pt-6">
        <p class="text-sm">© 2025 Dify2OpenAI</p>
        <p class="text-xs mt-2 text-gray-500">更新日期: 2025年4月5日</p>
      </div>
    </footer>

    <!-- 回到顶部按钮 -->
    <a href="#" id="backToTop"
      class="fixed bottom-6 right-6 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors duration-300 opacity-0 transform scale-90 hover:scale-100">
      <i class="fas fa-arrow-up"></i>
    </a>
  </div> <!-- max-w-6xl mx-auto 容器结束 -->

  <!-- 添加 JavaScript -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-bash.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-json.min.js"></script>
  <script>
    // 获取当前域名
    const currentDomain = window.location.host;

    // 替换所有curl示例中的域名
    function updateCurlExamples() {
      const codeBlocks = document.querySelectorAll('pre');
      codeBlocks.forEach(block => {
        // 替换curl命令行中的域名 (包含着色标签的情况)
        if (block.innerHTML.includes('<span class="text-pink-400">curl</span> http://localhost:3099')) {
          block.innerHTML = block.innerHTML.replace(
            /<span class="text-pink-400">curl<\/span> http:\/\/localhost:3099/g,
            `<span class="text-pink-400">curl</span> http://${currentDomain}`
          );
        }

        // 在请求体内替换所有域名引用（保持HTML标签结构）
        block.innerHTML = block.innerHTML.replace(
          /http:\/\/localhost:3099\/v1\/chat\/completions/g,
          `http://${currentDomain}/v1/chat/completions`
        );
      });

      // 运行一次Prism.js语法高亮
      if (typeof Prism !== 'undefined') {
        Prism.highlightAll();
      }
    }

    // 添加可编辑字段的功能
    function setupEditableFields() {
      const editableFields = document.querySelectorAll('.editable');

      editableFields.forEach(field => {
        // 加入编辑提示
        field.setAttribute('title', '点击编辑参数，示例将自动更新');

        // 在编辑时添加边框提示
        field.addEventListener('focus', function () {
          this.classList.add('ring-2', 'ring-blue-300');
        });

        field.addEventListener('blur', function () {
          // 移除边框
          this.classList.remove('ring-2', 'ring-blue-300');

          // 更新示例中的对应值
          const fieldId = this.id;
          const fieldValue = this.textContent.trim();

          // 所有参数变更都触发更新
          updateExamplesWithFieldValues();

          // 通知用户已更新
          showToast('参数已更新');
        });

        // 添加回车键处理
        field.addEventListener('keydown', function (e) {
          if (e.key === 'Enter') {
            e.preventDefault();
            this.blur(); // 失去焦点触发更新
          }
        });
      });
    }

    // 显示一个简单的提示消息
    function showToast(message) {
      // 移除任何已存在的提示
      const existingToast = document.getElementById('toast');
      if (existingToast) {
        existingToast.remove();
      }

      // 创建新提示
      const toast = document.createElement('div');
      toast.id = 'toast';
      toast.className = 'fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white px-4 py-2 rounded-full text-sm shadow-lg opacity-0 transition-opacity duration-300';
      toast.innerText = message;

      document.body.appendChild(toast);

      // 显示提示
      setTimeout(() => {
        toast.classList.replace('opacity-0', 'opacity-100');
      }, 10);

      // 2秒后隐藏提示
      setTimeout(() => {
        toast.classList.replace('opacity-100', 'opacity-0');
        setTimeout(() => {
          toast.remove();
        }, 300);
      }, 2000);
    }

    // 更新示例中的值
    function updateExamplesWithFieldValues() {
      // 在开始前保存增强后的HTML
      const codeBlocks = document.querySelectorAll('pre');
      const originalHTML = Array.from(codeBlocks).map(block => block.innerHTML);

      const difyApiUrl = document.getElementById('dify-api-url').textContent.trim();
      const apiKey = document.getElementById('api-key').textContent.trim();
      const botType = document.getElementById('bot-type').textContent.trim();
      const inputVariable = document.getElementById('input-variable').textContent.trim();
      const outputVariable = document.getElementById('output-variable').textContent.trim();

      // 更新方式一的授权示例 - 示例格式
      let authExample = document.querySelectorAll('.bg-gray-100');
      authExample.forEach(example => {
        if (example.textContent.includes('Authorization: Bearer') &&
          example.textContent.includes('DIFY_API_URL|API_KEY|BOT_TYPE')) {
          // 显示的示例参数
          example.innerHTML = `Authorization: Bearer DIFY_API_URL|API_KEY|BOT_TYPE|INPUT_VARIABLE|OUTPUT_VARIABLE`;
        } else if (example.textContent.includes('Authorization: Bearer') &&
          example.textContent.includes('https://cloud.dify.ai/v1|app-xxxx|Chat') ||
          example.textContent.includes(`${difyApiUrl}|${apiKey}|${botType}`)) {
          // 实际示例
          let varPart = '';
          if (botType.toLowerCase() === 'workflow') {
            varPart = `|${inputVariable}|${outputVariable}`;
          }
          example.innerHTML = `Authorization: Bearer ${difyApiUrl}|${apiKey}|${botType}${varPart}`;
        }
      });

      // 更新方式一的curl示例
      const method1Headers = document.querySelectorAll('pre');
      method1Headers.forEach(pre => {
        if (pre.innerHTML.includes('"Authorization: Bearer https://cloud.dify.ai/v1|app-xxxx|Chat"')) {
          let varPart = '';
          if (botType.toLowerCase() === 'workflow') {
            varPart = `|${inputVariable}|${outputVariable}`;
          }
          pre.innerHTML = pre.innerHTML.replace(
            /"Authorization: Bearer [^"]+"/g,
            `"Authorization: Bearer ${difyApiUrl}|${apiKey}|${botType}${varPart}"`
          );
        }
      });

      // 更新方式二的示例
      authExample.forEach(example => {
        // 更新 API_KEY 头部
        if (example.textContent.includes('Authorization: Bearer API_KEY')) {
          example.innerHTML = `Authorization: Bearer API_KEY`;
        } else if (example.textContent.includes('Authorization: Bearer app-xxxx')) {
          example.innerHTML = `Authorization: Bearer ${apiKey}`;
        }

        // 更新model参数格式
        if (example.textContent.includes('"model": "dify|BOT_TYPE|DIFY_API_URL')) {
          example.innerHTML = `"model": "dify|BOT_TYPE|DIFY_API_URL|INPUT_VARIABLE|OUTPUT_VARIABLE"`;
        }
      });

      // 更新方式二的curl示例
      const method2Headers = document.querySelectorAll('pre');
      method2Headers.forEach(pre => {
        if (pre.innerHTML.includes('"Authorization: Bearer app-xxxx"')) {
          pre.innerHTML = pre.innerHTML.replace(
            /"Authorization: Bearer [^"]+"/g,
            `"Authorization: Bearer ${apiKey}"`
          );
        }

        if (pre.innerHTML.includes('"model": "dify|Chat|https://cloud.dify.ai/v1"')) {
          let varPart = '';
          if (botType.toLowerCase() === 'workflow') {
            varPart = `|${inputVariable}|${outputVariable}`;
          }
          pre.innerHTML = pre.innerHTML.replace(
            /"model": "dify\|[^"\|]+\|[^"]+"/g,
            `"model": "dify|${botType}|${difyApiUrl}${varPart}"`
          );
        }
      });

      // 更新方式三的示例
      authExample.forEach(example => {
        // 更新 DIFY_API_URL 头部
        if (example.textContent.includes('Authorization: Bearer DIFY_API_URL')) {
          example.innerHTML = `Authorization: Bearer DIFY_API_URL`;
        } else if (example.textContent.includes('Authorization: Bearer https://cloud.dify.ai/v1') &&
          !example.textContent.includes('|')) {
          example.innerHTML = `Authorization: Bearer ${difyApiUrl}`;
        }

        // 更新model参数格式
        if (example.textContent.includes('"model": "dify|API_KEY|BOT_TYPE')) {
          example.innerHTML = `"model": "dify|API_KEY|BOT_TYPE|INPUT_VARIABLE|OUTPUT_VARIABLE"`;
        }
      });

      // 更新方式三的curl示例
      const method3Headers = document.querySelectorAll('pre');
      method3Headers.forEach(pre => {
        if (pre.innerHTML.includes('"Authorization: Bearer https://cloud.dify.ai/v1"') &&
          pre.innerHTML.includes('"model": "dify|app-xxxx|Chat"')) {
          pre.innerHTML = pre.innerHTML.replace(
            /"Authorization: Bearer [^"]+"/g,
            `"Authorization: Bearer ${difyApiUrl}"`
          );

          let varPart = '';
          if (botType.toLowerCase() === 'workflow') {
            varPart = `|${inputVariable}|${outputVariable}`;
          }
          pre.innerHTML = pre.innerHTML.replace(
            /"model": "dify\|[^"\|]+\|[^"\|]+"/g,
            `"model": "dify|${apiKey}|${botType}${varPart}"`
          );
        }
      });

      // 重新应用语法高亮
      if (typeof Prism !== 'undefined') {
        setTimeout(() => {
          Prism.highlightAll();
        }, 10);
      }
    }

    // 平滑滚动到锚点
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const targetId = this.getAttribute('href');
        const targetElement = document.querySelector(targetId);
        if (targetElement) {
          window.scrollTo({
            top: targetElement.offsetTop - 80,
            behavior: 'smooth'
          });
        }
      });
    });

    // 显示/隐藏回到顶部按钮
    const backToTopButton = document.getElementById('backToTop');

    window.addEventListener('scroll', function () {
      if (window.scrollY > 300) {
        backToTopButton.classList.remove('opacity-0');
        backToTopButton.classList.add('opacity-100');
      } else {
        backToTopButton.classList.remove('opacity-100');
        backToTopButton.classList.add('opacity-0');
      }
    });

    // 添加返回顶部点击事件
    backToTopButton.addEventListener('click', function (e) {
      e.preventDefault();
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function () {
      // 先更新参数
      setupEditableFields();
      updateExamplesWithFieldValues();

      // 再更新域名和运行Prism语法高亮
      setTimeout(() => {
        updateCurlExamples();

        // 确保语法高亮已应用
        if (typeof Prism !== 'undefined') {
          Prism.highlightAll();
        }
      }, 100);

      // 添加提示消息
      const editableFields = document.querySelectorAll('.editable');
      editableFields.forEach(field => {
        field.setAttribute('title', '点击编辑参数，示例将自动更新');
      });
    });
  </script>
</body>

</html>